// Smooth scrolling for navigation links
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-links a');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for sticky nav
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Active navigation highlighting
    function updateActiveNav() {
        const sections = document.querySelectorAll('.section');
        const navLinks = document.querySelectorAll('.nav-links a');
        
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop - 100;
            const sectionHeight = section.clientHeight;
            
            if (window.pageYOffset >= sectionTop && window.pageYOffset < sectionTop + sectionHeight) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    }

    // Scroll event listener for active nav
    window.addEventListener('scroll', updateActiveNav);

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.content-card, .feature-card, .timeline-content, .token-info, .chart-item');
    animateElements.forEach(el => {
        observer.observe(el);
    });

    // Parallax effect for background particles
    function parallaxEffect() {
        const scrolled = window.pageYOffset;
        const particles = document.querySelector('.floating-particles');
        
        if (particles) {
            const speed = scrolled * 0.5;
            particles.style.transform = `translateY(${speed}px)`;
        }
    }

    // Throttled scroll event for parallax
    let ticking = false;
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(parallaxEffect);
            ticking = true;
        }
    }

    window.addEventListener('scroll', function() {
        requestTick();
        ticking = false;
    });

    // Token badge pulse animation on scroll
    const tokenBadge = document.querySelector('.token-badge');
    if (tokenBadge) {
        window.addEventListener('scroll', function() {
            const scrollPercent = window.pageYOffset / (document.documentElement.scrollHeight - window.innerHeight);
            const pulseIntensity = 1 + (scrollPercent * 0.1);
            tokenBadge.style.transform = `scale(${pulseIntensity})`;
        });
    }

    // Dynamic text typing effect for hero title
    function typeWriter(element, text, speed = 100) {
        let i = 0;
        element.innerHTML = '';
        
        function type() {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }
        
        type();
    }

    // Initialize typing effect for main title
    const mainTitle = document.querySelector('.title-main');
    if (mainTitle) {
        const originalText = mainTitle.textContent;
        setTimeout(() => {
            typeWriter(mainTitle, originalText, 80);
        }, 500);
    }

    // Chart animation trigger
    function animateCharts() {
        const chartBars = document.querySelectorAll('.chart-bar');
        chartBars.forEach((bar, index) => {
            setTimeout(() => {
                bar.classList.add('animate-bar');
            }, index * 200);
        });
    }

    // Trigger chart animation when tokenomics section is visible
    const tokenomicsSection = document.querySelector('#tokenomics');
    if (tokenomicsSection) {
        const tokenomicsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCharts();
                    tokenomicsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.3 });
        
        tokenomicsObserver.observe(tokenomicsSection);
    }

    // Timeline animation
    function animateTimeline() {
        const timelineItems = document.querySelectorAll('.timeline-item');
        timelineItems.forEach((item, index) => {
            setTimeout(() => {
                item.classList.add('animate-timeline');
            }, index * 300);
        });
    }

    // Trigger timeline animation when roadmap section is visible
    const roadmapSection = document.querySelector('#roadmap');
    if (roadmapSection) {
        const roadmapObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateTimeline();
                    roadmapObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.2 });
        
        roadmapObserver.observe(roadmapSection);
    }

    // Mobile menu toggle
    function initMobileMenu() {
        const hamburger = document.querySelector('.hamburger');
        const navLinks = document.querySelector('.nav-links');
        const navLinksItems = document.querySelectorAll('.nav-links a');

        if (hamburger && navLinks) {
            hamburger.addEventListener('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                navLinks.classList.toggle('mobile-open');
                hamburger.innerHTML = navLinks.classList.contains('mobile-open') ? '✕' : '☰';
            });

            // Close menu when clicking on a link
            navLinksItems.forEach(link => {
                link.addEventListener('click', function() {
                    if (window.innerWidth <= 768) {
                        navLinks.classList.remove('mobile-open');
                        hamburger.innerHTML = '☰';
                    }
                });
            });

            // Close menu when clicking outside
            document.addEventListener('click', function(e) {
                if (!hamburger.contains(e.target) && !navLinks.contains(e.target)) {
                    navLinks.classList.remove('mobile-open');
                    hamburger.innerHTML = '☰';
                }
            });

            // Handle window resize
            window.addEventListener('resize', function() {
                if (window.innerWidth > 768) {
                    navLinks.classList.remove('mobile-open');
                    hamburger.innerHTML = '☰';
                }
            });
        }
    }

    // Initialize mobile menu
    initMobileMenu();

    // Add loading animation
    function addLoadingAnimation() {
        document.body.classList.add('loaded');
    }

    // Trigger loading animation
    setTimeout(addLoadingAnimation, 100);

    // Easter egg: Konami code
    let konamiCode = [];
    const konamiSequence = [38, 38, 40, 40, 37, 39, 37, 39, 66, 65]; // Up Up Down Down Left Right Left Right B A
    
    document.addEventListener('keydown', function(e) {
        konamiCode.push(e.keyCode);
        
        if (konamiCode.length > konamiSequence.length) {
            konamiCode.shift();
        }
        
        if (konamiCode.length === konamiSequence.length && 
            konamiCode.every((code, index) => code === konamiSequence[index])) {
            
            // Easter egg activated
            document.body.style.filter = 'hue-rotate(180deg)';
            setTimeout(() => {
                document.body.style.filter = '';
            }, 3000);
            
            // Show special message
            const message = document.createElement('div');
            message.textContent = '🎉 Aksara Easter Egg Activated! 🎉';
            message.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: var(--gradient-primary);
                color: var(--bg-dark);
                padding: 1rem 2rem;
                border-radius: 10px;
                font-weight: bold;
                z-index: 10000;
                animation: fadeInOut 3s ease-in-out;
            `;
            
            document.body.appendChild(message);
            
            setTimeout(() => {
                document.body.removeChild(message);
            }, 3000);
            
            konamiCode = [];
        }
    });
});

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-in {
        animation: slideInUp 0.8s ease-out forwards;
    }
    
    .animate-bar::before {
        animation: fillBar 2s ease-out forwards;
    }
    
    .animate-timeline {
        animation: timelineSlide 0.6s ease-out forwards;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes timelineSlide {
        from {
            opacity: 0;
            transform: translateX(-30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes fadeInOut {
        0%, 100% { opacity: 0; }
        50% { opacity: 1; }
    }
    

    
    body.loaded {
        animation: pageLoad 1s ease-out;
    }
    
    @keyframes pageLoad {
        from { opacity: 0; }
        to { opacity: 1; }
    }
`;

document.head.appendChild(style);
