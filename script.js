// Smooth scrolling for navigation links
document.addEventListener('DOMContentLoaded', function() {
    // Smooth scrolling for navigation links
    const navLinks = document.querySelectorAll('.nav-links a');
    
    navLinks.forEach(link => {
        link.addEventListener('click', function(e) {
            e.preventDefault();
            
            const targetId = this.getAttribute('href');
            const targetSection = document.querySelector(targetId);
            
            if (targetSection) {
                const offsetTop = targetSection.offsetTop - 80; // Account for sticky nav
                
                window.scrollTo({
                    top: offsetTop,
                    behavior: 'smooth'
                });
            }
        });
    });

    // Active navigation highlighting
    function updateActiveNav() {
        const sections = document.querySelectorAll('.section');
        const navLinks = document.querySelectorAll('.nav-links a');
        
        let current = '';
        
        sections.forEach(section => {
            const sectionTop = section.offsetTop - 100;
            const sectionHeight = section.clientHeight;
            
            if (window.pageYOffset >= sectionTop && window.pageYOffset < sectionTop + sectionHeight) {
                current = section.getAttribute('id');
            }
        });
        
        navLinks.forEach(link => {
            link.classList.remove('active');
            if (link.getAttribute('href') === `#${current}`) {
                link.classList.add('active');
            }
        });
    }

    // Scroll event listener for active nav
    window.addEventListener('scroll', updateActiveNav);

    // Intersection Observer for animations
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver(function(entries) {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.classList.add('animate-in');
            }
        });
    }, observerOptions);

    // Observe elements for animation
    const animateElements = document.querySelectorAll('.content-card, .feature-card, .timeline-content, .token-info, .chart-item');
    animateElements.forEach(el => {
        observer.observe(el);
    });

    // Parallax effect for background particles
    function parallaxEffect() {
        const scrolled = window.pageYOffset;
        const particles = document.querySelector('.floating-particles');
        
        if (particles) {
            const speed = scrolled * 0.5;
            particles.style.transform = `translateY(${speed}px)`;
        }
    }

    // Throttled scroll event for parallax
    let ticking = false;
    function requestTick() {
        if (!ticking) {
            requestAnimationFrame(parallaxEffect);
            ticking = true;
        }
    }

    window.addEventListener('scroll', function() {
        requestTick();
        ticking = false;
    });

    // Token badge pulse animation on scroll
    const tokenBadge = document.querySelector('.token-badge');
    if (tokenBadge) {
        window.addEventListener('scroll', function() {
            const scrollPercent = window.pageYOffset / (document.documentElement.scrollHeight - window.innerHeight);
            const pulseIntensity = 1 + (scrollPercent * 0.1);
            tokenBadge.style.transform = `scale(${pulseIntensity})`;
        });
    }

    // Dynamic text typing effect for hero title
    function typeWriter(element, text, speed = 100) {
        let i = 0;
        element.innerHTML = '';
        
        function type() {
            if (i < text.length) {
                element.innerHTML += text.charAt(i);
                i++;
                setTimeout(type, speed);
            }
        }
        
        type();
    }

    // Initialize typing effect for main title
    const mainTitle = document.querySelector('.title-main');
    if (mainTitle) {
        const originalText = mainTitle.textContent;
        setTimeout(() => {
            typeWriter(mainTitle, originalText, 80);
        }, 500);
    }

    // Chart animation trigger
    function animateCharts() {
        const chartBars = document.querySelectorAll('.chart-bar');
        chartBars.forEach((bar, index) => {
            setTimeout(() => {
                bar.classList.add('animate-bar');
            }, index * 200);
        });
    }

    // Trigger chart animation when tokenomics section is visible
    const tokenomicsSection = document.querySelector('#tokenomics');
    if (tokenomicsSection) {
        const tokenomicsObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateCharts();
                    tokenomicsObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.3 });
        
        tokenomicsObserver.observe(tokenomicsSection);
    }

    // Timeline animation
    function animateTimeline() {
        const timelineItems = document.querySelectorAll('.timeline-item');
        timelineItems.forEach((item, index) => {
            setTimeout(() => {
                item.classList.add('animate-timeline');
            }, index * 300);
        });
    }

    // Trigger timeline animation when roadmap section is visible
    const roadmapSection = document.querySelector('#roadmap');
    if (roadmapSection) {
        const roadmapObserver = new IntersectionObserver(function(entries) {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    animateTimeline();
                    roadmapObserver.unobserve(entry.target);
                }
            });
        }, { threshold: 0.2 });
        
        roadmapObserver.observe(roadmapSection);
    }

    // Mobile menu toggle (if needed in future)
    function createMobileMenu() {
        const nav = document.querySelector('.nav-sticky');
        const navLinks = document.querySelector('.nav-links');
        
        // Create hamburger button
        const hamburger = document.createElement('button');
        hamburger.className = 'hamburger';
        hamburger.innerHTML = '☰';
        hamburger.style.display = 'none';
        
        nav.querySelector('.container').appendChild(hamburger);
        
        hamburger.addEventListener('click', function() {
            navLinks.classList.toggle('mobile-open');
            hamburger.innerHTML = navLinks.classList.contains('mobile-open') ? '✕' : '☰';
        });
        
        // Show/hide hamburger based on screen size
        function checkScreenSize() {
            if (window.innerWidth <= 768) {
                hamburger.style.display = 'block';
                navLinks.classList.add('mobile-menu');
            } else {
                hamburger.style.display = 'none';
                navLinks.classList.remove('mobile-menu', 'mobile-open');
            }
        }
        
        window.addEventListener('resize', checkScreenSize);
        checkScreenSize();
    }

    // Initialize mobile menu
    createMobileMenu();

    // Add loading animation
    function addLoadingAnimation() {
        document.body.classList.add('loaded');
    }

    // Trigger loading animation
    setTimeout(addLoadingAnimation, 100);

    // Easter egg: Konami code
    let konamiCode = [];
    const konamiSequence = [38, 38, 40, 40, 37, 39, 37, 39, 66, 65]; // Up Up Down Down Left Right Left Right B A
    
    document.addEventListener('keydown', function(e) {
        konamiCode.push(e.keyCode);
        
        if (konamiCode.length > konamiSequence.length) {
            konamiCode.shift();
        }
        
        if (konamiCode.length === konamiSequence.length && 
            konamiCode.every((code, index) => code === konamiSequence[index])) {
            
            // Easter egg activated
            document.body.style.filter = 'hue-rotate(180deg)';
            setTimeout(() => {
                document.body.style.filter = '';
            }, 3000);
            
            // Show special message
            const message = document.createElement('div');
            message.textContent = '🎉 Aksara Easter Egg Activated! 🎉';
            message.style.cssText = `
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                background: var(--gradient-primary);
                color: var(--bg-dark);
                padding: 1rem 2rem;
                border-radius: 10px;
                font-weight: bold;
                z-index: 10000;
                animation: fadeInOut 3s ease-in-out;
            `;
            
            document.body.appendChild(message);
            
            setTimeout(() => {
                document.body.removeChild(message);
            }, 3000);
            
            konamiCode = [];
        }
    });
});

// Add CSS for animations
const style = document.createElement('style');
style.textContent = `
    .animate-in {
        animation: slideInUp 0.8s ease-out forwards;
    }
    
    .animate-bar::before {
        animation: fillBar 2s ease-out forwards;
    }
    
    .animate-timeline {
        animation: timelineSlide 0.6s ease-out forwards;
    }
    
    @keyframes slideInUp {
        from {
            opacity: 0;
            transform: translateY(30px);
        }
        to {
            opacity: 1;
            transform: translateY(0);
        }
    }
    
    @keyframes timelineSlide {
        from {
            opacity: 0;
            transform: translateX(-30px);
        }
        to {
            opacity: 1;
            transform: translateX(0);
        }
    }
    
    @keyframes fadeInOut {
        0%, 100% { opacity: 0; }
        50% { opacity: 1; }
    }
    
    .mobile-menu {
        position: absolute;
        top: 100%;
        left: 0;
        right: 0;
        background: rgba(10, 10, 10, 0.95);
        flex-direction: column;
        padding: 1rem;
        transform: translateY(-100%);
        opacity: 0;
        transition: all 0.3s ease;
    }
    
    .mobile-menu.mobile-open {
        transform: translateY(0);
        opacity: 1;
    }
    
    .hamburger {
        background: none;
        border: none;
        color: var(--text-light);
        font-size: 1.5rem;
        cursor: pointer;
        padding: 0.5rem;
        border-radius: 5px;
        transition: background 0.3s ease;
    }
    
    .hamburger:hover {
        background: rgba(0, 212, 255, 0.2);
    }
    
    body.loaded {
        animation: pageLoad 1s ease-out;
    }
    
    @keyframes pageLoad {
        from { opacity: 0; }
        to { opacity: 1; }
    }
`;

document.head.appendChild(style);
