/* Reset & Base Styles */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

:root {
    --primary-color: #00d4ff;
    --secondary-color: #0099cc;
    --accent-color: #ff6b35;
    --bg-dark: #0a0a0a;
    --bg-darker: #050505;
    --text-light: #ffffff;
    --text-gray: #b0b0b0;
    --text-dark: #333333;
    --gradient-primary: linear-gradient(135deg, #00d4ff 0%, #0099cc 100%);
    --gradient-secondary: linear-gradient(135deg, #ff6b35 0%, #f7931e 100%);
    --gradient-bg: linear-gradient(135deg, #0a0a0a 0%, #1a1a2e 50%, #16213e 100%);
    --shadow-glow: 0 0 20px rgba(0, 212, 255, 0.3);
    --shadow-card: 0 8px 32px rgba(0, 0, 0, 0.3);
}

html {
    scroll-behavior: smooth;
}

body {
    font-family: 'Inter', sans-serif;
    background: var(--gradient-bg);
    color: var(--text-light);
    line-height: 1.6;
    overflow-x: hidden;
}

.container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Background Animation */
.bg-animation {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    z-index: -1;
    overflow: hidden;
}

.floating-particles {
    position: absolute;
    width: 100%;
    height: 100%;
    background-image: 
        radial-gradient(2px 2px at 20px 30px, rgba(0, 212, 255, 0.3), transparent),
        radial-gradient(2px 2px at 40px 70px, rgba(255, 107, 53, 0.3), transparent),
        radial-gradient(1px 1px at 90px 40px, rgba(0, 212, 255, 0.5), transparent),
        radial-gradient(1px 1px at 130px 80px, rgba(255, 107, 53, 0.5), transparent);
    background-repeat: repeat;
    background-size: 200px 200px;
    animation: float 20s linear infinite;
}

@keyframes float {
    0% { transform: translateY(0px) translateX(0px); }
    33% { transform: translateY(-20px) translateX(10px); }
    66% { transform: translateY(-10px) translateX(-10px); }
    100% { transform: translateY(0px) translateX(0px); }
}

/* Hero Section */
.hero {
    min-height: 100vh;
    display: flex;
    align-items: center;
    justify-content: center;
    text-align: center;
    position: relative;
    background: linear-gradient(135deg, rgba(0, 212, 255, 0.1) 0%, rgba(255, 107, 53, 0.1) 100%);
}

.hero-content {
    max-width: 800px;
    animation: fadeInUp 1s ease-out;
}

.hero-title {
    margin-bottom: 2rem;
}

.title-main {
    display: block;
    font-family: 'Orbitron', monospace;
    font-size: clamp(2.5rem, 6vw, 4rem);
    font-weight: 900;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
    text-shadow: var(--shadow-glow);
}

.title-sub {
    display: block;
    font-size: clamp(1rem, 3vw, 1.5rem);
    font-weight: 400;
    color: var(--text-gray);
    line-height: 1.4;
}

.hero-slogan {
    font-size: clamp(1.2rem, 4vw, 2rem);
    font-weight: 600;
    color: var(--accent-color);
    margin-bottom: 2rem;
    font-style: italic;
}

.token-badge {
    display: inline-block;
    padding: 1rem 2rem;
    background: var(--gradient-primary);
    border-radius: 50px;
    box-shadow: var(--shadow-glow);
    animation: pulse 2s infinite;
}

.token-symbol {
    font-family: 'Orbitron', monospace;
    font-size: 1.5rem;
    font-weight: 700;
    color: var(--bg-dark);
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(30px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@keyframes pulse {
    0%, 100% { transform: scale(1); }
    50% { transform: scale(1.05); }
}

/* Navigation */
.nav-sticky {
    position: sticky;
    top: 0;
    background: rgba(10, 10, 10, 0.9);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid rgba(0, 212, 255, 0.3);
    z-index: 1000;
}

.nav-links {
    display: flex;
    justify-content: center;
    list-style: none;
    padding: 1rem 0;
    gap: 2rem;
}

.nav-links a {
    color: var(--text-light);
    text-decoration: none;
    font-weight: 500;
    padding: 0.5rem 1rem;
    border-radius: 25px;
    transition: all 0.3s ease;
    position: relative;
}

.nav-links a:hover {
    background: var(--gradient-primary);
    color: var(--bg-dark);
    transform: translateY(-2px);
}

/* Sections */
.section {
    padding: 5rem 0;
    position: relative;
}

.section-alt {
    background: rgba(255, 255, 255, 0.02);
}

.section-header {
    text-align: center;
    margin-bottom: 4rem;
}

.section-title {
    font-family: 'Orbitron', monospace;
    font-size: clamp(2rem, 5vw, 3rem);
    font-weight: 700;
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    margin-bottom: 1rem;
}

.section-subtitle {
    font-size: 1.2rem;
    color: var(--text-gray);
    font-weight: 300;
}

/* Content Cards */
.content-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 20px;
    padding: 3rem;
    box-shadow: var(--shadow-card);
    transition: transform 0.3s ease;
}

.content-card:hover {
    transform: translateY(-5px);
    box-shadow: 0 12px 40px rgba(0, 212, 255, 0.2);
}

.content-text {
    font-size: 1.1rem;
    line-height: 1.8;
    color: var(--text-light);
}

/* Feature Grid */
.feature-grid {
    display: grid;
    grid-template-columns: repeat(auto-fit, minmax(400px, 1fr));
    gap: 2rem;
    margin-top: 2rem;
}

.feature-card {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 20px;
    padding: 2.5rem;
    text-align: center;
    transition: all 0.3s ease;
    position: relative;
    overflow: hidden;
}

.feature-card::before {
    content: '';
    position: absolute;
    top: 0;
    left: -100%;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(0, 212, 255, 0.1), transparent);
    transition: left 0.5s ease;
}

.feature-card:hover::before {
    left: 100%;
}

.feature-card:hover {
    transform: translateY(-10px);
    border-color: var(--primary-color);
    box-shadow: 0 15px 50px rgba(0, 212, 255, 0.3);
}

.feature-icon {
    font-size: 3rem;
    margin-bottom: 1.5rem;
}

.feature-card h3 {
    font-family: 'Orbitron', monospace;
    font-size: 1.3rem;
    color: var(--primary-color);
    margin-bottom: 1rem;
}

.feature-card h4 {
    color: var(--accent-color);
    margin-bottom: 1rem;
    font-size: 1.1rem;
}

.feature-card p {
    color: var(--text-gray);
    line-height: 1.6;
    margin-bottom: 1rem;
}

/* Tokenomics */
.tokenomics-grid {
    display: grid;
    grid-template-columns: 1fr 2fr;
    gap: 3rem;
    align-items: start;
}

.token-info {
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 20px;
    padding: 2rem;
}

.token-detail {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1);
}

.token-detail:last-child {
    border-bottom: none;
}

.label {
    color: var(--text-gray);
    font-weight: 500;
}

.value {
    color: var(--primary-color);
    font-weight: 600;
    font-family: 'Orbitron', monospace;
}

.allocation-chart {
    display: flex;
    flex-direction: column;
    gap: 1.5rem;
}

.chart-item {
    display: flex;
    align-items: center;
    gap: 1rem;
}

.chart-bar {
    position: relative;
    width: 200px;
    height: 40px;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 20px;
    overflow: hidden;
}

.chart-bar::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    height: 100%;
    width: var(--percentage);
    background: var(--gradient-primary);
    border-radius: 20px;
    animation: fillBar 2s ease-out;
}

.percentage {
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    font-weight: 700;
    color: var(--bg-dark);
    font-family: 'Orbitron', monospace;
    z-index: 1;
}

.chart-label h4 {
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-family: 'Orbitron', monospace;
}

.chart-label p {
    color: var(--text-gray);
    font-size: 0.9rem;
}

@keyframes fillBar {
    from { width: 0; }
    to { width: var(--percentage); }
}

/* Roadmap Timeline */
.roadmap-timeline {
    position: relative;
    max-width: 800px;
    margin: 0 auto;
}

.roadmap-timeline::before {
    content: '';
    position: absolute;
    left: 50%;
    top: 0;
    bottom: 0;
    width: 2px;
    background: var(--gradient-primary);
    transform: translateX(-50%);
}

.timeline-item {
    position: relative;
    margin-bottom: 3rem;
    display: flex;
    align-items: center;
}

.timeline-item:nth-child(odd) {
    flex-direction: row;
}

.timeline-item:nth-child(even) {
    flex-direction: row-reverse;
}

.timeline-marker {
    position: absolute;
    left: 50%;
    width: 20px;
    height: 20px;
    background: var(--gradient-primary);
    border-radius: 50%;
    transform: translateX(-50%);
    box-shadow: var(--shadow-glow);
    z-index: 1;
}

.timeline-content {
    width: 45%;
    background: rgba(255, 255, 255, 0.05);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(0, 212, 255, 0.2);
    border-radius: 15px;
    padding: 2rem;
    position: relative;
}

.timeline-item:nth-child(odd) .timeline-content {
    margin-right: auto;
}

.timeline-item:nth-child(even) .timeline-content {
    margin-left: auto;
}

.timeline-content h3 {
    font-family: 'Orbitron', monospace;
    color: var(--primary-color);
    margin-bottom: 0.5rem;
    font-size: 1.3rem;
}

.timeline-status {
    display: inline-block;
    background: var(--gradient-secondary);
    color: var(--text-light);
    padding: 0.3rem 1rem;
    border-radius: 15px;
    font-size: 0.8rem;
    font-weight: 600;
    margin-bottom: 1rem;
}

.timeline-content ul {
    list-style: none;
    padding-left: 0;
}

.timeline-content li {
    position: relative;
    padding-left: 1.5rem;
    margin-bottom: 0.5rem;
    color: var(--text-gray);
}

.timeline-content li::before {
    content: '▶';
    position: absolute;
    left: 0;
    color: var(--accent-color);
    font-size: 0.8rem;
}

/* Footer */
.footer {
    background: var(--bg-darker);
    padding: 2rem 0;
    text-align: center;
    border-top: 1px solid rgba(0, 212, 255, 0.3);
}

.footer p {
    color: var(--text-gray);
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-links {
        flex-wrap: wrap;
        gap: 1rem;
    }

    .nav-links a {
        font-size: 0.9rem;
        padding: 0.4rem 0.8rem;
    }

    .feature-grid {
        grid-template-columns: 1fr;
    }

    .feature-card {
        min-width: auto;
    }

    .tokenomics-grid {
        grid-template-columns: 1fr;
        gap: 2rem;
    }

    .chart-item {
        flex-direction: column;
        text-align: center;
        gap: 1rem;
    }

    .chart-bar {
        width: 100%;
        max-width: 300px;
    }

    .roadmap-timeline::before {
        left: 20px;
    }

    .timeline-item {
        flex-direction: column !important;
        align-items: flex-start;
        padding-left: 50px;
    }

    .timeline-marker {
        left: 20px;
    }

    .timeline-content {
        width: 100%;
        margin: 0 !important;
    }

    .section {
        padding: 3rem 0;
    }

    .content-card {
        padding: 2rem;
    }
}

@media (max-width: 480px) {
    .container {
        padding: 0 15px;
    }

    .hero-content {
        padding: 0 1rem;
    }

    .token-badge {
        padding: 0.8rem 1.5rem;
    }

    .token-symbol {
        font-size: 1.2rem;
    }

    .content-card {
        padding: 1.5rem;
    }

    .feature-card {
        padding: 2rem;
    }

    .timeline-content {
        padding: 1.5rem;
    }
}
